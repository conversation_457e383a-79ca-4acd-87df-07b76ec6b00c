# Real Estate Backend - Detailed API Documentation

## 🚀 Server Information
- **Base URL**: `http://localhost:8080/api/v1`
- **Health Check**: `http://localhost:8080/health`
- **Authentication**: Bearer JWT Token
- **Content-Type**: `application/json`

## 🔐 Authentication Endpoints

### 1. User Registration
**Endpoint**: `POST /api/v1/auth/register`

**Request Payload**:
```json
{
  "username": "johndo<PERSON>",           // Required, string, 3-50 chars
  "email": "<EMAIL>",     // Required, valid email
  "password": "password123",       // Required, min 6 chars
  "firstName": "John",             // Required, string
  "lastName": "Doe",               // Required, string
  "phoneNumber": "**********"      // Required, string
}
```

**Success Response (201)**:
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.abc123",
  "type": "Bearer",
  "username": "johndo<PERSON>",
  "email": "<EMAIL>",
  "role": "USER",
  "message": "Tài khoản đã được tạo và đăng nhập thành công!"
}
```

**Error Response (400)**:
```json
{
  "error": "Bad Request",
  "message": "Username already exists"
}
```

### 2. User Login
**Endpoint**: `POST /api/v1/auth/login`

**Request Payload**:
```json
{
  "username": "johndoe",           // Required, string
  "password": "password123"        // Required, string
}
```

**Success Response (200)**:
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "type": "Bearer",
  "username": "johndoe",
  "email": "<EMAIL>",
  "role": "USER",
  "message": "Đăng nhập thành công!"
}
```

**Error Response (401)**:
```json
{
  "error": "Unauthorized",
  "message": "Invalid username or password"
}
```

## 📊 Public Data Endpoints

### 3. Get Categories
**Endpoint**: `GET /api/v1/categories`

**Request**: No payload required

**Success Response (200)**:
```json
[
  {
    "id": 1,
    "name": "Apartment",
    "description": "Apartment and condominium listings",
    "icon": "apartment",
    "created_at": "2025-07-08T16:30:53.59Z",
    "updated_at": "2025-07-08T16:30:53.59Z"
  },
  {
    "id": 2,
    "name": "House",
    "description": "Single-family houses and townhouses",
    "icon": "house",
    "created_at": "2025-07-08T16:30:53.59Z",
    "updated_at": "2025-07-08T16:30:53.59Z"
  }
  // ... 4 more categories
]
```

### 4. Get Membership Plans
**Endpoint**: `GET /api/v1/memberships`

**Request**: No payload required

**Success Response (200)**:
```json
[
  {
    "id": 1,
    "name": "BASIC",
    "description": "Basic membership - Post properties and manage listings",
    "price": 99.00,
    "duration_months": 1,
    "max_properties": 10,
    "featured_properties": 0,
    "priority_support": false,
    "analytics_access": false,
    "multiple_images": false,
    "contact_info_display": true,
    "ai_content_generation": false,
    "push_top_limit": 0,
    "type": "BASIC",
    "is_active": true,
    "created_at": "2025-07-08T16:30:53.59Z",
    "updated_at": "2025-07-08T16:30:53.59Z"
  },
  {
    "id": 2,
    "name": "ADVANCED",
    "description": "Advanced membership - AI SEO optimization, Push top 10 posts, Premium features",
    "price": 299.00,
    "duration_months": 1,
    "max_properties": 50,
    "featured_properties": 10,
    "priority_support": true,
    "analytics_access": true,
    "multiple_images": true,
    "contact_info_display": true,
    "ai_content_generation": true,
    "push_top_limit": 10,
    "type": "PREMIUM",
    "is_active": true,
    "created_at": "2025-07-08T16:30:53.59Z",
    "updated_at": "2025-07-08T16:30:53.59Z"
  }
]
```

### 5. Get Public Properties
**Endpoint**: `GET /api/v1/properties`

**Query Parameters**:
- `page` (optional): Page number, default 0
- `size` (optional): Page size, default 10
- `propertyType` (optional): APARTMENT, HOUSE, VILLA, LAND, OFFICE, SHOP
- `listingType` (optional): SALE, RENT
- `city` (optional): City name
- `district` (optional): District name
- `minPrice` (optional): Minimum price
- `maxPrice` (optional): Maximum price
- `bedrooms` (optional): Number of bedrooms
- `sortBy` (optional): created_at, price, property_area
- `sortDir` (optional): asc, desc

**Success Response (200)**:
```json
{
  "content": [
    {
      "id": 1,
      "user_id": 2,
      "category_id": 1,
      "title": "Beautiful Apartment in District 1",
      "description": "A stunning 3-bedroom apartment with city view",
      "price": 750000.00,
      "address": "123 Nguyen Hue Street",
      "city": "Ho Chi Minh City",
      "district": "District 1",
      "ward": "",
      "latitude": null,
      "longitude": null,
      "property_area": 120.5,
      "land_area": null,
      "bedrooms": 3,
      "bathrooms": 2,
      "floors": null,
      "property_type": "APARTMENT",
      "listing_type": "SALE",
      "status": "APPROVED",
      "is_featured": false,
      "view_count": 0,
      "contact_count": 0,
      "published_at": null,
      "expires_at": null,
      "created_at": "2025-07-08T16:47:49.436Z",
      "updated_at": "2025-07-08T16:47:49.436Z",
      "category": {
        "id": 1,
        "name": "Apartment",
        "description": "Apartment and condominium listings",
        "icon": "apartment"
      },
      "user": {
        "id": 2,
        "username": "johndoe",
        "first_name": "John",
        "last_name": "Doe",
        "phone_number": "**********"
      },
      "images": []
    }
  ],
  "pageable": {
    "page_number": 0,
    "page_size": 10
  },
  "total_elements": 1,
  "total_pages": 1
}
```

### 6. Get Property by ID
**Endpoint**: `GET /api/v1/properties/{id}`

**Path Parameters**:
- `id`: Property ID (integer)

**Success Response (200)**:
```json
{
  "id": 1,
  "user_id": 2,
  "category_id": 1,
  "title": "Beautiful Apartment in District 1",
  "description": "A stunning 3-bedroom apartment with city view",
  "price": 750000.00,
  "address": "123 Nguyen Hue Street",
  "city": "Ho Chi Minh City",
  "district": "District 1",
  "ward": "",
  "latitude": null,
  "longitude": null,
  "property_area": 120.5,
  "land_area": null,
  "bedrooms": 3,
  "bathrooms": 2,
  "floors": null,
  "property_type": "APARTMENT",
  "listing_type": "SALE",
  "status": "APPROVED",
  "is_featured": false,
  "view_count": 15,
  "contact_count": 3,
  "published_at": "2025-07-08T16:47:49.436Z",
  "expires_at": null,
  "created_at": "2025-07-08T16:47:49.436Z",
  "updated_at": "2025-07-08T16:47:49.436Z",
  "category": {
    "id": 1,
    "name": "Apartment",
    "description": "Apartment and condominium listings",
    "icon": "apartment"
  },
  "user": {
    "id": 2,
    "username": "johndoe",
    "first_name": "John",
    "last_name": "Doe",
    "phone_number": "**********",
    "email": "<EMAIL>"
  },
  "images": [
    {
      "id": 1,
      "property_id": 1,
      "image_url": "https://example.com/images/property1_1.jpg",
      "alt_text": "Living room view",
      "display_order": 1,
      "is_primary": true
    }
  ]
}
```

**Error Response (404)**:
```json
{
  "error": "Not Found",
  "message": "Property not found"
}
```

### 7. Search Properties
**Endpoint**: `GET /api/v1/properties/search`

**Query Parameters**:
- `query` (optional): Search text for title/description
- `city` (optional): City name
- `district` (optional): District name
- `propertyType` (optional): APARTMENT, HOUSE, VILLA, LAND, OFFICE, SHOP
- `listingType` (optional): SALE, RENT
- `minPrice` (optional): Minimum price
- `maxPrice` (optional): Maximum price
- `bedrooms` (optional): Number of bedrooms
- `page` (optional): Page number, default 0
- `size` (optional): Page size, default 10

**Success Response (200)**:
```json
{
  "content": [
    // Array of property objects (same structure as Get Properties)
  ],
  "pageable": {
    "page_number": 0,
    "page_size": 10
  },
  "total_elements": 5,
  "total_pages": 1
}
```

### 8. Get Featured Properties
**Endpoint**: `GET /api/v1/properties/featured`

**Query Parameters**:
- `limit` (optional): Number of properties to return, default 10

**Success Response (200)**:
```json
[
  {
    "id": 1,
    "title": "Luxury Apartment in District 1",
    "price": 750000.00,
    "property_type": "APARTMENT",
    "listing_type": "SALE",
    "is_featured": true,
    "city": "Ho Chi Minh City",
    "district": "District 1",
    "bedrooms": 3,
    "bathrooms": 2,
    "property_area": 120.5,
    "images": [
      {
        "image_url": "https://example.com/images/property1_1.jpg",
        "is_primary": true
      }
    ]
  }
]
```

## 🏠 Property Management (Protected Endpoints)

### 9. Create Property
**Endpoint**: `POST /api/v1/properties`
**Authentication**: Required (Bearer Token)

**Request Payload**:
```json
{
  "title": "Beautiful Apartment in District 1",           // Required, string, max 255 chars
  "description": "A stunning 3-bedroom apartment",       // Required, string
  "price": 750000.00,                                    // Required, number, > 0
  "address": "123 Nguyen Hue Street",                    // Required, string
  "city": "Ho Chi Minh City",                            // Required, string
  "district": "District 1",                              // Required, string
  "ward": "Ward 1",                                      // Optional, string
  "latitude": 10.7769,                                   // Optional, number
  "longitude": 106.7009,                                 // Optional, number
  "categoryId": 1,                                       // Required, integer, valid category ID
  "propertyType": "APARTMENT",                           // Required, enum: APARTMENT|HOUSE|VILLA|LAND|OFFICE|SHOP
  "listingType": "SALE",                                 // Required, enum: SALE|RENT
  "bedrooms": 3,                                         // Optional, integer, >= 0
  "bathrooms": 2,                                        // Optional, integer, >= 0
  "floors": 1,                                           // Optional, integer, >= 0
  "propertyArea": 120.5,                                 // Optional, number, > 0
  "landArea": 150.0                                      // Optional, number, > 0
}
```

**Success Response (201)**:
```json
{
  "id": 4,
  "user_id": 5,
  "category_id": 1,
  "title": "Beautiful Apartment in District 1",
  "description": "A stunning 3-bedroom apartment",
  "price": 750000.00,
  "address": "123 Nguyen Hue Street",
  "city": "Ho Chi Minh City",
  "district": "District 1",
  "ward": "Ward 1",
  "latitude": 10.7769,
  "longitude": 106.7009,
  "property_area": 120.5,
  "land_area": 150.0,
  "bedrooms": 3,
  "bathrooms": 2,
  "floors": 1,
  "property_type": "APARTMENT",
  "listing_type": "SALE",
  "status": "PENDING",
  "is_featured": false,
  "view_count": 0,
  "contact_count": 0,
  "published_at": null,
  "expires_at": null,
  "created_at": "2025-07-08T16:47:49.436Z",
  "updated_at": "2025-07-08T16:47:49.436Z",
  "message": "Property created successfully and is pending approval"
}
```

**Error Response (400)**:
```json
{
  "error": "Bad Request",
  "message": "Property limit reached for your membership plan"
}
```

**Error Response (401)**:
```json
{
  "error": "Unauthorized",
  "message": "Invalid or missing token"
}
```

### 10. Update Property
**Endpoint**: `PUT /api/v1/properties/{id}`
**Authentication**: Required (Bearer Token)

**Path Parameters**:
- `id`: Property ID (integer)

**Request Payload**: Same as Create Property (all fields optional for update)

**Success Response (200)**:
```json
{
  "id": 4,
  "title": "Updated Beautiful Apartment",
  "description": "Updated description",
  "price": 800000.00,
  // ... other property fields
  "updated_at": "2025-07-08T17:00:00.000Z",
  "message": "Property updated successfully"
}
```

**Error Response (403)**:
```json
{
  "error": "Forbidden",
  "message": "You can only update your own properties"
}
```

### 11. Delete Property
**Endpoint**: `DELETE /api/v1/properties/{id}`
**Authentication**: Required (Bearer Token)

**Path Parameters**:
- `id`: Property ID (integer)

**Success Response (200)**:
```json
{
  "message": "Property deleted successfully",
  "propertyId": 4
}
```

**Error Response (403)**:
```json
{
  "error": "Forbidden",
  "message": "You can only delete your own properties"
}
```

### 12. Get User Properties
**Endpoint**: `GET /api/v1/my-properties`
**Authentication**: Required (Bearer Token)

**Query Parameters**:
- `page` (optional): Page number, default 0
- `size` (optional): Page size, default 10

**Success Response (200)**:
```json
{
  "content": [
    {
      "id": 4,
      "title": "Beautiful Apartment in District 1",
      "status": "PENDING",
      "price": 750000.00,
      "property_type": "APARTMENT",
      "listing_type": "SALE",
      "created_at": "2025-07-08T16:47:49.436Z",
      "view_count": 0,
      "contact_count": 0,
      "is_featured": false
    }
  ],
  "pageable": {
    "page_number": 0,
    "page_size": 10
  },
  "total_elements": 1,
  "total_pages": 1
}
```

### 13. Boost Property
**Endpoint**: `POST /api/v1/properties/{id}/boost`
**Authentication**: Required (Bearer Token)

**Path Parameters**:
- `id`: Property ID (integer)

**Request**: No payload required

**Success Response (200)**:
```json
{
  "message": "Property boosted successfully",
  "propertyId": 4,
  "boostDuration": "7 days",
  "creditsRemaining": 9
}
```

**Error Response (400)**:
```json
{
  "error": "Bad Request",
  "message": "no boost credits remaining"
}
```

**Error Response (403)**:
```json
{
  "error": "Forbidden",
  "message": "you can only boost your own properties"
}
```

### 14. Upload Property Images
**Endpoint**: `POST /api/v1/properties/{id}/upload-images`
**Authentication**: Required (Bearer Token)
**Content-Type**: `multipart/form-data`

**Path Parameters**:
- `id`: Property ID (integer)

**Request Payload** (multipart/form-data):
```
files: [File, File, ...] // Multiple image files
altTexts: ["Living room", "Kitchen", ...] // Optional alt texts
```

**Success Response (201)**:
```json
{
  "message": "Images uploaded successfully",
  "propertyId": 4,
  "images": [
    {
      "id": 1,
      "property_id": 4,
      "image_url": "https://s3.amazonaws.com/bucket/property4_1.jpg",
      "alt_text": "Living room",
      "display_order": 1,
      "is_primary": true,
      "created_at": "2025-07-08T17:00:00.000Z"
    },
    {
      "id": 2,
      "property_id": 4,
      "image_url": "https://s3.amazonaws.com/bucket/property4_2.jpg",
      "alt_text": "Kitchen",
      "display_order": 2,
      "is_primary": false,
      "created_at": "2025-07-08T17:00:00.000Z"
    }
  ]
}
```

## 📱 User Dashboard

### 15. Get User Dashboard
**Endpoint**: `GET /api/v1/dashboard`
**Authentication**: Required (Bearer Token)

**Request**: No payload required

**Success Response (200)**:
```json
{
  "user": {
    "id": 5,
    "username": "johndoe",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "phoneNumber": "**********",
    "role": "USER",
    "status": "ACTIVE",
    "emailVerified": false,
    "createdAt": "2025-07-08T16:47:46.146Z"
  },
  "membership": {
    "id": 1,
    "planName": "BASIC",
    "price": 99.00,
    "maxProperties": 10,
    "propertiesUsed": 1,
    "propertiesRemaining": 9,
    "hasAiGeneration": false,
    "pushTopLimit": 0,
    "pushTopUsed": 0,
    "pushTopRemaining": 0,
    "startDate": "2025-07-08T16:47:46.146Z",
    "endDate": "2025-08-08T16:47:46.146Z",
    "daysRemaining": 30,
    "status": "ACTIVE",
    "autoRenewal": false
  },
  "properties": {
    "totalProperties": 1,
    "approvedProperties": 0,
    "pendingProperties": 1,
    "rejectedProperties": 0,
    "featuredProperties": 0
  },
  "monthlyUsage": {
    "month": "2025-07",
    "propertiesCreated": 1,
    "pushTopUsed": 0,
    "aiContentUsed": 0,
    "totalViews": 0,
    "totalContacts": 0
  },
  "recentActivity": [
    {
      "type": "PROPERTY_CREATED",
      "message": "Property 'Beautiful Apartment' created",
      "timestamp": "2025-07-08T16:47:49.436Z"
    }
  ]
}
```

## 💳 Payment Endpoints

### 16. Create Checkout Session
**Endpoint**: `POST /api/v1/payments/checkout`
**Authentication**: Required (Bearer Token)

**Request Payload**:
```json
{
  "membershipId": 2,                                     // Required, integer, valid membership ID
  "successUrl": "http://localhost:3000/payment/success", // Required, string, valid URL
  "cancelUrl": "http://localhost:3000/payment/cancel"    // Required, string, valid URL
}
```

**Success Response (200)**:
```json
{
  "sessionId": "cs_test_a1B2c3D4e5F6g7H8i9J0k1L2m3N4o5P6q7R8s9T0",
  "url": "https://checkout.stripe.com/pay/cs_test_a1B2c3D4e5F6g7H8i9J0k1L2m3N4o5P6q7R8s9T0",
  "membershipId": 2,
  "amount": 299.00,
  "currency": "USD"
}
```

**Error Response (400)**:
```json
{
  "error": "Bad Request",
  "message": "Invalid membership ID"
}
```

### 17. Get User Payments
**Endpoint**: `GET /api/v1/payments/my-payments`
**Authentication**: Required (Bearer Token)

**Query Parameters**:
- `page` (optional): Page number, default 0
- `size` (optional): Page size, default 10
- `status` (optional): PENDING, COMPLETED, FAILED, REFUNDED

**Success Response (200)**:
```json
{
  "content": [
    {
      "id": 1,
      "user_id": 5,
      "membership_id": 2,
      "stripe_session_id": "cs_test_a1B2c3D4e5F6g7H8i9J0k1L2m3N4o5P6q7R8s9T0",
      "stripe_payment_intent_id": "pi_1234567890abcdef",
      "amount": 299.00,
      "currency": "USD",
      "status": "COMPLETED",
      "payment_method": "card",
      "created_at": "2025-07-08T17:00:00.000Z",
      "updated_at": "2025-07-08T17:00:00.000Z",
      "membership": {
        "id": 2,
        "name": "ADVANCED",
        "description": "Advanced membership with premium features"
      }
    }
  ],
  "pageable": {
    "page_number": 0,
    "page_size": 10
  },
  "total_elements": 1,
  "total_pages": 1
}
```

### 18. Stripe Webhook
**Endpoint**: `POST /api/v1/payments/webhook`
**Authentication**: Stripe signature verification

**Request Headers**:
- `Stripe-Signature`: Webhook signature from Stripe

**Request Payload**: Stripe webhook event payload

**Success Response (200)**:
```json
{
  "received": true
}
```

## 🤖 AI Features

### 19. Chatbot Message
**Endpoint**: `POST /api/v1/chatbot/message`
**Authentication**: Required (Bearer Token)

**Request Payload**:
```json
{
  "message": "I am looking for a 2-bedroom apartment in District 1 under $600,000", // Required, string
  "conversationId": "conv-123"                                                      // Optional, string
}
```

**Success Response (200)**:
```json
{
  "response": "I found several 2-bedroom apartments in District 1 within your budget. Here are some options: ...",
  "conversationId": "conv-123",
  "suggestions": [
    "Show me apartments with balcony",
    "What about 3-bedroom options?",
    "Properties near metro stations"
  ],
  "properties": [
    {
      "id": 1,
      "title": "Cozy 2BR Apartment",
      "price": 550000.00,
      "district": "District 1",
      "bedrooms": 2
    }
  ]
}
```

### 20. Property Recommendations
**Endpoint**: `POST /api/v1/chatbot/recommendations`
**Authentication**: Required (Bearer Token)

**Request Payload**:
```json
{
  "city": "Ho Chi Minh City",        // Optional, string
  "district": "District 1",          // Optional, string
  "propertyType": "APARTMENT",       // Optional, enum
  "listingType": "SALE",             // Optional, enum
  "minPrice": 300000,                // Optional, number
  "maxPrice": 700000,                // Optional, number
  "bedrooms": 2,                     // Optional, integer
  "bathrooms": 2,                    // Optional, integer
  "minArea": 80,                     // Optional, number
  "maxArea": 150,                    // Optional, number
  "amenities": ["parking", "gym"]    // Optional, array of strings
}
```

**Success Response (200)**:
```json
{
  "recommendations": [
    {
      "id": 1,
      "title": "Modern 2BR Apartment",
      "price": 650000.00,
      "property_type": "APARTMENT",
      "listing_type": "SALE",
      "city": "Ho Chi Minh City",
      "district": "District 1",
      "bedrooms": 2,
      "bathrooms": 2,
      "property_area": 120.5,
      "match_score": 95,
      "match_reasons": [
        "Perfect location match",
        "Within price range",
        "Exact bedroom count"
      ],
      "images": [
        {
          "image_url": "https://example.com/image1.jpg",
          "is_primary": true
        }
      ]
    }
  ],
  "total_matches": 5,
  "search_criteria": {
    "city": "Ho Chi Minh City",
    "district": "District 1",
    "propertyType": "APARTMENT",
    "priceRange": "300000-700000",
    "bedrooms": 2
  }
}
```

## 📈 Analytics

### 21. Property Analytics
**Endpoint**: `GET /api/v1/analytics/properties`
**Authentication**: Required (Bearer Token)

**Query Parameters**:
- `period` (optional): daily, weekly, monthly, yearly, custom (default: monthly)
- `startDate` (optional): Start date for custom period (YYYY-MM-DD)
- `endDate` (optional): End date for custom period (YYYY-MM-DD)
- `propertyId` (optional): Specific property ID for detailed analytics

**Success Response (200)**:
```json
{
  "period": "monthly",
  "startDate": "2025-07-01",
  "endDate": "2025-07-31",
  "userId": 5,
  "summary": {
    "totalViews": 150,
    "totalContacts": 25,
    "totalProperties": 3,
    "featuredProperties": 1,
    "conversionRate": 16.7,
    "averageViewsPerProperty": 50.0
  },
  "topProperty": {
    "id": 1,
    "title": "Beautiful Apartment in District 1",
    "views": 75,
    "contacts": 15,
    "conversionRate": 20.0
  },
  "dailyStats": [
    {
      "date": "2025-07-01",
      "views": 5,
      "contacts": 1
    },
    {
      "date": "2025-07-02",
      "views": 8,
      "contacts": 2
    }
  ],
  "propertyBreakdown": [
    {
      "propertyId": 1,
      "title": "Beautiful Apartment",
      "views": 75,
      "contacts": 15,
      "conversionRate": 20.0
    },
    {
      "propertyId": 2,
      "title": "Cozy House",
      "views": 45,
      "contacts": 8,
      "conversionRate": 17.8
    }
  ]
}
```

### 22. User Analytics
**Endpoint**: `GET /api/v1/analytics/users`
**Authentication**: Required (Bearer Token)

**Query Parameters**:
- `period` (optional): daily, weekly, monthly, yearly, custom (default: weekly)
- `startDate` (optional): Start date for custom period
- `endDate` (optional): End date for custom period

**Success Response (200)**:
```json
{
  "period": "weekly",
  "startDate": "2025-07-01",
  "endDate": "2025-07-07",
  "userId": 5,
  "membershipUsage": {
    "planName": "BASIC",
    "maxProperties": 10,
    "propertiesUsed": 3,
    "propertiesRemaining": 7,
    "pushTopLimit": 0,
    "pushTopUsed": 0,
    "pushTopRemaining": 0,
    "usagePercentage": 30.0
  },
  "activitySummary": {
    "propertiesPosted": 2,
    "propertiesApproved": 1,
    "propertiesPending": 1,
    "totalViews": 120,
    "totalContacts": 18,
    "averageViewsPerProperty": 40.0,
    "conversionRate": 15.0
  },
  "dailyActivity": [
    {
      "date": "2025-07-01",
      "propertiesCreated": 1,
      "views": 20,
      "contacts": 3
    },
    {
      "date": "2025-07-02",
      "propertiesCreated": 0,
      "views": 25,
      "contacts": 4
    }
  ],
  "recommendations": [
    "Consider upgrading to ADVANCED plan for more properties",
    "Add more images to increase property views",
    "Update property descriptions for better SEO"
  ]
}
```

## 👑 Admin Endpoints (Admin Only)

### 23. Get Admin Dashboard Stats
**Endpoint**: `GET /api/v1/admin/dashboard`
**Authentication**: Required (Admin Bearer Token)

**Request**: No payload required

**Success Response (200)**:
```json
{
  "overview": {
    "totalUsers": 150,
    "totalProperties": 1250,
    "pendingProperties": 25,
    "approvedProperties": 1100,
    "rejectedProperties": 125,
    "totalRevenue": 15750.00,
    "activeSubscriptions": 120
  },
  "recentActivity": [
    {
      "type": "USER_REGISTERED",
      "message": "New user 'johndoe' registered",
      "timestamp": "2025-07-08T17:00:00.000Z"
    },
    {
      "type": "PROPERTY_SUBMITTED",
      "message": "Property 'Beautiful Apartment' submitted for review",
      "timestamp": "2025-07-08T16:45:00.000Z"
    }
  ],
  "monthlyStats": {
    "newUsers": 25,
    "newProperties": 180,
    "revenue": 2980.00,
    "subscriptions": 15
  },
  "topCategories": [
    {
      "categoryId": 1,
      "name": "Apartment",
      "propertyCount": 450,
      "percentage": 36.0
    },
    {
      "categoryId": 2,
      "name": "House",
      "propertyCount": 380,
      "percentage": 30.4
    }
  ]
}
```

**Error Response (403)**:
```json
{
  "error": "Forbidden",
  "message": "Admin access required"
}
```

### 24. Get Pending Properties
**Endpoint**: `GET /api/v1/admin/properties/pending`
**Authentication**: Required (Admin Bearer Token)

**Query Parameters**:
- `page` (optional): Page number, default 0
- `size` (optional): Page size, default 10

**Success Response (200)**:
```json
{
  "content": [
    {
      "id": 4,
      "user_id": 5,
      "title": "Beautiful Apartment in District 1",
      "description": "A stunning 3-bedroom apartment",
      "price": 750000.00,
      "property_type": "APARTMENT",
      "listing_type": "SALE",
      "city": "Ho Chi Minh City",
      "district": "District 1",
      "status": "PENDING",
      "created_at": "2025-07-08T16:47:49.436Z",
      "user": {
        "id": 5,
        "username": "johndoe",
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe"
      },
      "category": {
        "id": 1,
        "name": "Apartment"
      }
    }
  ],
  "pageable": {
    "page_number": 0,
    "page_size": 10
  },
  "total_elements": 25,
  "total_pages": 3
}
```

### 25. Approve Property
**Endpoint**: `POST /api/v1/admin/properties/{id}/approve`
**Authentication**: Required (Admin Bearer Token)

**Path Parameters**:
- `id`: Property ID (integer)

**Request Payload**:
```json
{
  "isFeatured": true,                                    // Optional, boolean, default false
  "adminNote": "Property approved and featured",        // Optional, string
  "publishedAt": "2025-07-08T17:00:00.000Z"            // Optional, ISO datetime
}
```

**Success Response (200)**:
```json
{
  "message": "Property approved successfully",
  "propertyId": 4,
  "status": "APPROVED",
  "isFeatured": true,
  "publishedAt": "2025-07-08T17:00:00.000Z",
  "adminNote": "Property approved and featured"
}
```

### 26. Reject Property
**Endpoint**: `POST /api/v1/admin/properties/{id}/reject`
**Authentication**: Required (Admin Bearer Token)

**Path Parameters**:
- `id`: Property ID (integer)

**Request Payload**:
```json
{
  "reason": "Property does not meet our quality standards", // Required, string
  "adminNote": "Missing required documentation"             // Optional, string
}
```

**Success Response (200)**:
```json
{
  "message": "Property rejected successfully",
  "propertyId": 4,
  "status": "REJECTED",
  "reason": "Property does not meet our quality standards",
  "adminNote": "Missing required documentation",
  "rejectedAt": "2025-07-08T17:00:00.000Z"
}
```

### 27. Get All Users
**Endpoint**: `GET /api/v1/admin/users`
**Authentication**: Required (Admin Bearer Token)

**Query Parameters**:
- `page` (optional): Page number, default 0
- `size` (optional): Page size, default 10
- `status` (optional): ACTIVE, BANNED, INACTIVE
- `role` (optional): USER, ADMIN
- `search` (optional): Search by username or email

**Success Response (200)**:
```json
{
  "content": [
    {
      "id": 5,
      "username": "johndoe",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "phone_number": "**********",
      "role": "USER",
      "status": "ACTIVE",
      "email_verified": false,
      "created_at": "2025-07-08T16:47:46.146Z",
      "last_login": "2025-07-08T16:50:00.000Z",
      "membership": {
        "id": 1,
        "name": "BASIC",
        "status": "ACTIVE",
        "end_date": "2025-08-08T16:47:46.146Z"
      },
      "stats": {
        "totalProperties": 3,
        "approvedProperties": 1,
        "pendingProperties": 2,
        "totalViews": 120,
        "totalContacts": 18
      }
    }
  ],
  "pageable": {
    "page_number": 0,
    "page_size": 10
  },
  "total_elements": 150,
  "total_pages": 15
}
```

### 28. Ban User
**Endpoint**: `POST /api/v1/admin/users/{id}/ban`
**Authentication**: Required (Admin Bearer Token)

**Path Parameters**:
- `id`: User ID (integer)

**Request Payload**:
```json
{
  "reason": "Violation of terms of service",    // Optional, string
  "duration": 30,                               // Optional, integer (days), null for permanent
  "adminNote": "Multiple spam property posts"   // Optional, string
}
```

**Success Response (200)**:
```json
{
  "message": "User banned successfully",
  "userId": 5,
  "status": "BANNED",
  "reason": "Violation of terms of service",
  "bannedUntil": "2025-08-08T17:00:00.000Z",
  "adminNote": "Multiple spam property posts"
}
```

### 29. Unban User
**Endpoint**: `POST /api/v1/admin/users/{id}/unban`
**Authentication**: Required (Admin Bearer Token)

**Path Parameters**:
- `id`: User ID (integer)

**Request Payload**:
```json
{
  "adminNote": "User appeal approved"    // Optional, string
}
```

**Success Response (200)**:
```json
{
  "message": "User unbanned successfully",
  "userId": 5,
  "status": "ACTIVE",
  "adminNote": "User appeal approved",
  "unbannedAt": "2025-07-08T17:00:00.000Z"
}
```

## 🔧 Error Handling

### HTTP Status Codes
- **200 OK**: Request successful
- **201 Created**: Resource created successfully
- **400 Bad Request**: Invalid request data or validation error
- **401 Unauthorized**: Invalid or missing authentication token
- **403 Forbidden**: Insufficient permissions for the requested action
- **404 Not Found**: Requested resource not found
- **409 Conflict**: Resource already exists or conflict with current state
- **422 Unprocessable Entity**: Request data is valid but cannot be processed
- **500 Internal Server Error**: Server error

### Error Response Format
All error responses follow this format:
```json
{
  "error": "Error Type",
  "message": "Detailed error message",
  "code": "ERROR_CODE",           // Optional, specific error code
  "details": {                    // Optional, additional error details
    "field": "validation error"
  },
  "timestamp": "2025-07-08T17:00:00.000Z"
}
```

### Common Error Examples

**Validation Error (400)**:
```json
{
  "error": "Bad Request",
  "message": "Validation failed",
  "details": {
    "title": "Title is required",
    "price": "Price must be greater than 0"
  }
}
```

**Authentication Error (401)**:
```json
{
  "error": "Unauthorized",
  "message": "Invalid or expired token"
}
```

**Permission Error (403)**:
```json
{
  "error": "Forbidden",
  "message": "Admin access required"
}
```

**Resource Not Found (404)**:
```json
{
  "error": "Not Found",
  "message": "Property not found"
}
```

**Rate Limit Error (429)**:
```json
{
  "error": "Too Many Requests",
  "message": "Rate limit exceeded. Try again in 60 seconds",
  "retryAfter": 60
}
```

## 🎯 Integration Guidelines

### Authentication Flow
1. **Registration/Login**: Get JWT token from auth endpoints
2. **Token Storage**: Store token securely (localStorage/sessionStorage)
3. **API Calls**: Include token in Authorization header: `Bearer YOUR_TOKEN`
4. **Token Refresh**: Handle token expiration (redirect to login)

### Property Workflow
1. **Create Property**: Status starts as "PENDING"
2. **Admin Review**: Admin approves/rejects property
3. **Public Visibility**: Only "APPROVED" properties appear in public listings
4. **User Management**: Users can only modify their own properties

### Payment Integration
1. **Create Checkout**: Call `/payments/checkout` with membership ID
2. **Redirect User**: Redirect to Stripe checkout URL from response
3. **Handle Callbacks**: Implement success/cancel URL handlers
4. **Webhook Processing**: Set up webhook endpoint for payment confirmations

### Membership System
- **BASIC Plan**: $99/month, 10 properties, no boost credits
- **ADVANCED Plan**: $299/month, 50 properties, 10 boost credits, AI features
- **Auto-Assignment**: New users get BASIC membership automatically
- **Usage Tracking**: Monitor property and boost credit usage

### File Upload
- **Supported Formats**: JPEG, PNG, WebP
- **Size Limits**: Max 5MB per image, max 10 images per property
- **Storage**: Images stored in AWS S3
- **Optimization**: Images automatically resized and optimized

## 🚀 Production Deployment

### Environment Variables
```bash
# Database
DB_HOST=your-db-host
DB_PORT=5432
DB_NAME=realestate_membership
DB_USER=your-db-user
DB_PASSWORD=your-db-password

# JWT
JWT_SECRET=your-jwt-secret-key

# Stripe
STRIPE_SECRET_KEY=sk_live_your_stripe_secret
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# AWS S3
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=ap-southeast-2
AWS_S3_BUCKET=your-s3-bucket

# OpenAI
OPENAI_API_KEY=your-openai-api-key

# Server
PORT=8080
GIN_MODE=release
```

### Health Check
Monitor server health using:
```bash
curl http://your-domain.com/health
```

Expected response:
```json
{
  "status": "ok",
  "timestamp": "2025-07-08T17:00:00.000Z",
  "version": "1.0.0"
}
```

## 📊 API Testing

### Test with curl
```bash
# Set base URL
BASE_URL="http://localhost:8080/api/v1"

# Register user
curl -X POST $BASE_URL/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","email":"<EMAIL>","password":"password123","firstName":"Test","lastName":"User","phoneNumber":"**********"}'

# Login and get token
TOKEN=$(curl -s -X POST $BASE_URL/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","password":"password123"}' | jq -r '.token')

# Create property
curl -X POST $BASE_URL/properties \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"title":"Test Property","description":"Test description","price":500000,"address":"123 Test St","city":"Ho Chi Minh City","district":"District 1","categoryId":1,"propertyType":"APARTMENT","listingType":"SALE"}'
```

### Test Payment (Stripe Test Mode)
Use test card: ********************
- Expiry: Any future date
- CVC: Any 3 digits
- ZIP: Any 5 digits

## 🎉 Summary

This API provides a complete real estate platform with:
- ✅ **32 endpoints** covering all functionality
- ✅ **JWT authentication** with role-based access
- ✅ **Stripe payment integration** with test card support
- ✅ **AI-powered features** (chatbot, recommendations, analytics)
- ✅ **Admin panel** for property and user management
- ✅ **File upload** with AWS S3 integration
- ✅ **Comprehensive error handling** and validation
- ✅ **Production-ready** with proper security and monitoring

**🚀 Ready for frontend integration!**
