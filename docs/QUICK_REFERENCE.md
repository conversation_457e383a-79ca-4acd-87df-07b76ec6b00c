# Real Estate API - Quick Reference

## 🚀 Base URL
`http://localhost:8080/api/v1`

## 🔐 Authentication
Include in headers: `Authorization: Bearer YOUR_JWT_TOKEN`

## 📋 Endpoint Summary

### Public Endpoints (No Auth Required)
```bash
GET  /health                           # Health check
POST /api/v1/auth/register            # User registration  
POST /api/v1/auth/login               # User login
GET  /api/v1/categories               # Get categories
GET  /api/v1/memberships              # Get membership plans
GET  /api/v1/properties               # Get public properties
GET  /api/v1/properties/{id}          # Get property by ID
GET  /api/v1/properties/search        # Search properties
GET  /api/v1/properties/featured      # Get featured properties
```

### User Endpoints (Auth Required)
```bash
GET  /api/v1/dashboard                # User dashboard
POST /api/v1/properties               # Create property
PUT  /api/v1/properties/{id}          # Update property
DELETE /api/v1/properties/{id}        # Delete property
GET  /api/v1/my-properties            # Get user properties
POST /api/v1/properties/{id}/boost    # Boost property
POST /api/v1/properties/{id}/upload-images # Upload images
POST /api/v1/payments/checkout        # Create payment session
GET  /api/v1/payments/my-payments     # Get user payments
POST /api/v1/chatbot/message          # Chat with AI
POST /api/v1/chatbot/recommendations  # Get AI recommendations
GET  /api/v1/analytics/properties     # Property analytics
GET  /api/v1/analytics/users          # User analytics
```

### Admin Endpoints (Admin Auth Required)
```bash
GET  /api/v1/admin/dashboard          # Admin dashboard
GET  /api/v1/admin/properties/pending # Pending properties
POST /api/v1/admin/properties/{id}/approve # Approve property
POST /api/v1/admin/properties/{id}/reject  # Reject property
GET  /api/v1/admin/users              # Get all users
POST /api/v1/admin/users/{id}/ban     # Ban user
POST /api/v1/admin/users/{id}/unban   # Unban user
```

## 🎯 Quick Examples

### 1. Register & Login
```bash
# Register
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "johndoe",
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "John",
    "lastName": "Doe",
    "phoneNumber": "**********"
  }'

# Login
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "johndoe",
    "password": "password123"
  }'
```

### 2. Create Property
```bash
curl -X POST http://localhost:8080/api/v1/properties \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "title": "Beautiful Apartment",
    "description": "3-bedroom apartment with city view",
    "price": 750000,
    "address": "123 Main Street",
    "city": "Ho Chi Minh City",
    "district": "District 1",
    "categoryId": 1,
    "propertyType": "APARTMENT",
    "listingType": "SALE",
    "bedrooms": 3,
    "bathrooms": 2,
    "propertyArea": 120.5
  }'
```

### 3. Search Properties
```bash
curl "http://localhost:8080/api/v1/properties/search?query=apartment&city=Ho%20Chi%20Minh%20City&propertyType=APARTMENT&minPrice=500000&maxPrice=1000000&bedrooms=2&page=0&size=10"
```

### 4. Create Payment Session
```bash
curl -X POST http://localhost:8080/api/v1/payments/checkout \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "membershipId": 2,
    "successUrl": "http://localhost:3000/success",
    "cancelUrl": "http://localhost:3000/cancel"
  }'
```

### 5. Chat with AI
```bash
curl -X POST http://localhost:8080/api/v1/chatbot/message \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "message": "I am looking for a 2-bedroom apartment under $600,000",
    "conversationId": "conv-123"
  }'
```

## 💳 Payment Testing
**Test Card**: `****************`
- Expiry: Any future date
- CVC: Any 3 digits
- ZIP: Any 5 digits

## 📊 Membership Plans
- **BASIC**: $99/month, 10 properties, no boost
- **ADVANCED**: $299/month, 50 properties, 10 boosts, AI features

## 🏠 Property Types
- `APARTMENT` - Apartments and condominiums
- `HOUSE` - Single-family houses
- `VILLA` - Luxury villas
- `LAND` - Land plots
- `OFFICE` - Office spaces
- `SHOP` - Commercial shops

## 📍 Listing Types
- `SALE` - Properties for sale
- `RENT` - Properties for rent

## 📈 Property Status
- `PENDING` - Awaiting admin approval
- `APPROVED` - Approved and public
- `REJECTED` - Rejected by admin

## 🔧 Common Query Parameters

### Pagination
- `page`: Page number (default: 0)
- `size`: Page size (default: 10)

### Property Filters
- `propertyType`: APARTMENT, HOUSE, VILLA, LAND, OFFICE, SHOP
- `listingType`: SALE, RENT
- `city`: City name
- `district`: District name
- `minPrice`: Minimum price
- `maxPrice`: Maximum price
- `bedrooms`: Number of bedrooms
- `bathrooms`: Number of bathrooms

### Analytics Periods
- `period`: daily, weekly, monthly, yearly, custom
- `startDate`: Start date (YYYY-MM-DD)
- `endDate`: End date (YYYY-MM-DD)

## ⚠️ Error Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Server Error

## 🎯 Response Formats

### Success Response
```json
{
  "id": 1,
  "title": "Property Title",
  "status": "APPROVED",
  "message": "Operation successful"
}
```

### Error Response
```json
{
  "error": "Bad Request",
  "message": "Detailed error message",
  "details": {
    "field": "validation error"
  }
}
```

### Paginated Response
```json
{
  "content": [...],
  "pageable": {
    "page_number": 0,
    "page_size": 10
  },
  "total_elements": 50,
  "total_pages": 5
}
```

## 🚀 Getting Started

1. **Start Server**:
   ```bash
   cd real-estate-core
   make run
   ```

2. **Health Check**:
   ```bash
   curl http://localhost:8080/health
   ```

3. **Register User**:
   ```bash
   curl -X POST http://localhost:8080/api/v1/auth/register \
     -H "Content-Type: application/json" \
     -d '{"username":"test","email":"<EMAIL>","password":"password123","firstName":"Test","lastName":"User","phoneNumber":"**********"}'
   ```

4. **Get Token & Start Using API**

## 📚 Full Documentation
- **Detailed API Docs**: `DETAILED_API_DOCUMENTATION.md`
- **Integration Guide**: `API_INTEGRATION_GUIDE.md`
- **Test Results**: `TEST_RESULTS_SUMMARY.md`

**🎉 Ready to integrate with your frontend!**
