import { Property } from './property';

export interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'bot';
  timestamp: Date;
  type: 'text' | 'property_results' | 'suggestions' | 'error';
  data?: any; // For property results or other structured data
}

export interface ChatbotMessage {
  message: string;
  conversationId?: string;
}

export interface ChatbotResponse {
  response: string;
  conversationId: string;
  suggestions?: string[];
}

export interface PropertyRecommendationRequest {
  city?: string;
  district?: string;
  property_type?: string;
  listing_type?: string;
  minPrice?: number;
  maxPrice?: number;
  bedrooms?: number;
}

export interface PropertySearchResult {
  properties: Property[];
  total: number;
  query: string;
}

export interface ChatSuggestion {
  text: string;
  action: string;
}

export interface ChatbotState {
  isOpen: boolean;
  messages: ChatMessage[];
  isTyping: boolean;
  suggestions: ChatSuggestion[];
  conversationId?: string;
}
