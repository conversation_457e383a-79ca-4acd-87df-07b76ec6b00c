export interface Membership {
  id: number;
  name: string;
  description: string;
  price: number;
  duration_months: number;
  max_properties: number;
  featured_properties: number;
  priority_support: boolean;
  analytics_access: boolean;
  multiple_images: boolean;
  contact_info_display: boolean;
  ai_content_generation: boolean;
  push_top_limit: number;
  type: 'BASIC' | 'PREMIUM';
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface UserMembership {
  id: number;
  userId: number;
  membershipId: number;
  startDate: string;
  endDate: string;
  status: MembershipStatus;
  paymentId?: number;
  autoRenew: boolean;
  createdAt: string;
  updatedAt: string;
  membership?: Membership;
}

export enum MembershipStatus {
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED',
  CANCELLED = 'CANCELLED',
  PENDING = 'PENDING'
}

// Payment related types
export interface Payment {
  id: number;
  user_id: number;
  membership_id: number;
  amount: number;
  currency: string;
  status: PaymentStatus;
  payment_method: string;
  stripe_session_id?: string;
  created_at: string;
  updated_at: string;
  membership?: Membership;
}

export enum PaymentStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

// Stripe specific types
export interface StripePaymentRequest {
  membershipId: number;
  successUrl: string;
  cancelUrl: string;
}

export interface StripePaymentResponse {
  sessionId: string;
  url: string;
}

// Dashboard data
export interface UserDashboard {
  membership: {
    planName: string;
    maxProperties: number;
    propertiesUsed: number;
    propertiesRemaining: number;
    hasAiGeneration: boolean;
    pushTopLimit: number;
    daysRemaining: number;
  };
  properties: {
    totalProperties: number;
    approvedProperties: number;
    pendingProperties: number;
    rejectedProperties: number;
  };
  monthlyUsage: {
    month: string;
    pushTopUsed: number;
    aiContentUsed: number;
  };
}

// Membership benefits
export const MEMBERSHIP_BENEFITS = {
  BASIC: [
    'Post up to 10 properties',
    'Basic property management',
    'Standard support',
    'Contact info display'
  ],
  PREMIUM: [
    'Post up to 50 properties',
    'Advanced property management',
    'Priority support',
    'Advanced analytics',
    'Featured listings (10)',
    'Multiple images per property',
    'AI content generation',
    'Push top 10 posts per month'
  ]
};

// Membership colors for UI
export const MEMBERSHIP_COLORS = {
  BASIC: {
    primary: '#3B82F6',
    secondary: '#EFF6FF',
    text: '#1E40AF'
  },
  PREMIUM: {
    primary: '#F59E0B',
    secondary: '#FEF3C7',
    text: '#D97706'
  }
};
