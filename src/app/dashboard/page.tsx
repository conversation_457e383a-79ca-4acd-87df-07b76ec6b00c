'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { dashboardApi } from '@/lib/api';
import { errorUtils } from '@/lib/auth';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import Header from '@/components/layout/Header';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Home,
  User,
  LogOut,
  Building,
  Eye,
  MessageSquare,
  Calendar,
  TrendingUp,
  Crown,
  Zap,
  CheckCircle,
  Clock,
  XCircle,
  Plus
} from 'lucide-react';
import { toast } from 'sonner';
import { UserDashboard } from '@/types/membership';

export default function DashboardPage() {
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState<UserDashboard | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);
      const data = await dashboardApi.getUserDashboard();
      setDashboardData(data);
    } catch (error) {
      const errorMessage = errorUtils.getErrorMessage(error);
      toast.error(`Failed to load dashboard: ${errorMessage}`);
      // Set mock data for demo purposes
      setDashboardData({
        membership: {
          planName: 'BASIC',
          maxProperties: 10,
          propertiesUsed: 0,
          propertiesRemaining: 10,
          hasAiGeneration: false,
          pushTopLimit: 0,
          daysRemaining: 30
        },
        properties: {
          totalProperties: 0,
          approvedProperties: 0,
          pendingProperties: 0,
          rejectedProperties: 0
        },
        monthlyUsage: {
          month: new Date().toISOString().slice(0, 7),
          pushTopUsed: 0,
          aiContentUsed: 0
        }
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gray-50">
          <Header />
          <div className="container mx-auto px-4 py-8">
            <div className="space-y-6">
              <Skeleton className="h-8 w-64" />
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {[...Array(8)].map((_, i) => (
                  <Skeleton key={i} className="h-32" />
                ))}
              </div>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <Header />

        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600 mt-2">
              Welcome back, {user?.firstName || user?.username}! Here's your property overview.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Membership Status */}
            <Card className="col-span-full lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Crown className="h-5 w-5 mr-2 text-yellow-600" />
                  Membership Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-semibold">{dashboardData?.membership.planName} Plan</span>
                    <Badge variant={dashboardData?.membership.planName === 'PREMIUM' ? 'default' : 'secondary'}>
                      {dashboardData?.membership.planName}
                    </Badge>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Properties Used</span>
                      <span>{dashboardData?.membership.propertiesUsed} / {dashboardData?.membership.maxProperties}</span>
                    </div>
                    <Progress
                      value={(dashboardData?.membership.propertiesUsed || 0) / (dashboardData?.membership.maxProperties || 1) * 100}
                      className="h-2"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Days Remaining</p>
                      <p className="font-semibold">{dashboardData?.membership.daysRemaining}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">AI Generation</p>
                      <p className="font-semibold">
                        {dashboardData?.membership.hasAiGeneration ? 'Available' : 'Not Available'}
                      </p>
                    </div>
                  </div>

                  <Button asChild className="w-full">
                    <Link href="/membership">Upgrade Plan</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="col-span-full lg:col-span-2">
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-3">
                  <Button variant="outline" asChild>
                    <Link href="/properties/add" className="flex items-center gap-2">
                      <Plus className="h-4 w-4" />
                      Add Property
                    </Link>
                  </Button>
                  <Button variant="outline" asChild>
                    <Link href="/properties/my-properties" className="flex items-center gap-2">
                      <Building className="h-4 w-4" />
                      My Properties
                    </Link>
                  </Button>
                  <Button variant="outline" asChild>
                    <Link href="/membership/payments" className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Payment History
                    </Link>
                  </Button>
                  <Button variant="outline" asChild>
                    <Link href="/profile" className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      Edit Profile
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Property Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Properties</CardTitle>
                <Building className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData?.properties.totalProperties}</div>
                <p className="text-xs text-muted-foreground">
                  {dashboardData?.membership.propertiesRemaining} remaining
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Approved</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{dashboardData?.properties.approvedProperties}</div>
                <p className="text-xs text-muted-foreground">
                  Live properties
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pending</CardTitle>
                <Clock className="h-4 w-4 text-yellow-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">{dashboardData?.properties.pendingProperties}</div>
                <p className="text-xs text-muted-foreground">
                  Awaiting approval
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Rejected</CardTitle>
                <XCircle className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{dashboardData?.properties.rejectedProperties}</div>
                <p className="text-xs text-muted-foreground">
                  Need revision
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Monthly Usage */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2" />
                  Monthly Usage
                </CardTitle>
                <CardDescription>
                  {dashboardData?.monthlyUsage.month}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Push Top Used</span>
                      <span>{dashboardData?.monthlyUsage.pushTopUsed} / {dashboardData?.membership.pushTopLimit}</span>
                    </div>
                    <Progress
                      value={dashboardData?.membership.pushTopLimit ?
                        (dashboardData?.monthlyUsage.pushTopUsed / dashboardData?.membership.pushTopLimit) * 100 : 0
                      }
                      className="h-2"
                    />
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>AI Content Generated</span>
                      <span>{dashboardData?.monthlyUsage.aiContentUsed}</span>
                    </div>
                    <div className="text-xs text-gray-600">
                      {dashboardData?.membership.hasAiGeneration ? 'Unlimited' : 'Not available in your plan'}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Getting Started</CardTitle>
                <CardDescription>
                  Complete these steps to maximize your success
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm">Account created</span>
                  </div>
                  <div className="flex items-center gap-3">
                    {dashboardData?.properties.totalProperties ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <div className="h-4 w-4 rounded-full border-2 border-gray-300" />
                    )}
                    <span className="text-sm">Add your first property</span>
                  </div>
                  <div className="flex items-center gap-3">
                    {dashboardData?.membership.planName === 'PREMIUM' ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <div className="h-4 w-4 rounded-full border-2 border-gray-300" />
                    )}
                    <span className="text-sm">Upgrade to Premium</span>
                  </div>
                </div>

                {!dashboardData?.properties.totalProperties && (
                  <Button asChild className="w-full mt-4">
                    <Link href="/properties/add">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Your First Property
                    </Link>
                  </Button>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
