'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { paymentApi, publicApi } from '@/lib/api';
import { errorUtils } from '@/lib/auth';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import Header from '@/components/layout/Header';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  ArrowLeft, 
  Check, 
  CreditCard, 
  Shield, 
  Crown,
  Star,
  Users
} from 'lucide-react';
import { toast } from 'sonner';
import { Membership } from '@/types/membership';

export default function CheckoutPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  const [membership, setMembership] = useState<Membership | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);

  const membershipId = searchParams.get('membership');

  useEffect(() => {
    if (membershipId) {
      fetchMembership();
    } else {
      router.push('/membership');
    }
  }, [membershipId]);

  const fetchMembership = async () => {
    try {
      setIsLoading(true);
      const memberships = await publicApi.getMemberships();
      const selectedMembership = memberships.find(m => m.id === parseInt(membershipId!));
      
      if (selectedMembership) {
        setMembership(selectedMembership);
      } else {
        toast.error('Membership plan not found');
        router.push('/membership');
      }
    } catch (error) {
      const errorMessage = errorUtils.getErrorMessage(error);
      toast.error(`Failed to load membership: ${errorMessage}`);
      router.push('/membership');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStripeCheckout = async () => {
    if (!membership) return;

    try {
      setIsProcessing(true);
      
      const successUrl = `${window.location.origin}/membership/payment/success`;
      const cancelUrl = `${window.location.origin}/membership/payment/cancel`;
      
      const session = await paymentApi.createCheckoutSession(
        membership.id,
        successUrl,
        cancelUrl
      );

      // Redirect to Stripe Checkout
      window.location.href = session.url;
      
    } catch (error) {
      const errorMessage = errorUtils.getErrorMessage(error);
      toast.error(`Payment failed: ${errorMessage}`);
    } finally {
      setIsProcessing(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);
  };

  const getMembershipFeatures = (membership: Membership) => {
    const features = [
      `Post up to ${membership.max_properties} properties`,
      membership.multiple_images ? 'Multiple images per property' : 'Single image per property',
      membership.contact_info_display ? 'Contact info display' : 'Basic contact display',
    ];

    if (membership.featured_properties > 0) {
      features.push(`${membership.featured_properties} featured property slots`);
    }

    if (membership.priority_support) {
      features.push('Priority customer support');
    }

    if (membership.analytics_access) {
      features.push('Advanced analytics access');
    }

    if (membership.ai_content_generation) {
      features.push('AI content generation');
    }

    if (membership.push_top_limit > 0) {
      features.push(`Push top ${membership.push_top_limit} posts per month`);
    }

    return features;
  };

  const getPlanIcon = (type: string) => {
    switch (type) {
      case 'BASIC':
        return <Users className="h-8 w-8" />;
      case 'PREMIUM':
        return <Crown className="h-8 w-8" />;
      default:
        return <Star className="h-8 w-8" />;
    }
  };

  if (isLoading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gray-50">
          <Header />
          <div className="container mx-auto px-4 py-8">
            <div className="max-w-2xl mx-auto space-y-6">
              <Skeleton className="h-8 w-64" />
              <Skeleton className="h-96" />
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  if (!membership) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gray-50">
          <Header />
          <div className="container mx-auto px-4 py-8">
            <div className="max-w-2xl mx-auto text-center">
              <h1 className="text-2xl font-bold text-gray-900 mb-4">Membership Not Found</h1>
              <Button onClick={() => router.push('/membership')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Memberships
              </Button>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <Header />
        
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            {/* Header */}
            <div className="mb-8">
              <Button 
                variant="ghost" 
                onClick={() => router.push('/membership')}
                className="mb-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Plans
              </Button>
              <h1 className="text-3xl font-bold text-gray-900">Complete Your Purchase</h1>
              <p className="text-gray-600 mt-2">
                You're about to subscribe to the {membership.name} plan
              </p>
            </div>

            {/* Order Summary */}
            <Card className="mb-8">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <div className={`p-3 rounded-full ${membership.type === 'PREMIUM' ? 'bg-yellow-100 text-yellow-600' : 'bg-blue-100 text-blue-600'}`}>
                    {getPlanIcon(membership.type)}
                  </div>
                  <div>
                    <span className="text-2xl">{membership.name}</span>
                    <Badge className="ml-2" variant={membership.type === 'PREMIUM' ? 'default' : 'secondary'}>
                      {membership.type}
                    </Badge>
                  </div>
                </CardTitle>
                <CardDescription>
                  {membership.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center text-lg">
                    <span>Monthly subscription</span>
                    <span className="font-bold">{formatPrice(membership.price)}</span>
                  </div>
                  
                  <div className="border-t pt-4">
                    <h4 className="font-semibold mb-3">What's included:</h4>
                    <ul className="space-y-2">
                      {getMembershipFeatures(membership).map((feature, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-gray-700">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Payment Method */}
            <Card className="mb-8">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Payment Method
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-3 p-4 border rounded-lg bg-blue-50 border-blue-200">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-8 bg-blue-600 rounded flex items-center justify-center">
                        <CreditCard className="h-4 w-4 text-white" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium">Stripe Checkout</h4>
                      <p className="text-sm text-gray-600">
                        Secure payment with credit card, debit card, or digital wallet
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Shield className="h-4 w-4" />
                    <span>Your payment information is secure and encrypted</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Total and Checkout */}
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <div className="flex justify-between items-center text-xl font-bold">
                    <span>Total</span>
                    <span>{formatPrice(membership.price)}/month</span>
                  </div>
                  
                  <div className="text-sm text-gray-600">
                    <p>• Billing cycle: Monthly</p>
                    <p>• You can cancel anytime</p>
                    <p>• No setup fees or hidden charges</p>
                  </div>
                  
                  <Button 
                    className="w-full" 
                    size="lg"
                    onClick={handleStripeCheckout}
                    disabled={isProcessing}
                  >
                    {isProcessing ? (
                      'Processing...'
                    ) : (
                      <>
                        <CreditCard className="h-4 w-4 mr-2" />
                        Complete Purchase - {formatPrice(membership.price)}
                      </>
                    )}
                  </Button>
                  
                  <p className="text-xs text-gray-500 text-center">
                    By completing this purchase, you agree to our Terms of Service and Privacy Policy.
                    Your subscription will automatically renew monthly.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
