import axios, { AxiosInstance, AxiosResponse } from 'axios';
import Cookies from 'js-cookie';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api/v1';

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000,
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = Cookies.get('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      Cookies.remove('auth_token');
      Cookies.remove('user_data');
      window.location.href = '/auth/login';
    }
    return Promise.reject(error);
  }
);

// ===== TYPES =====

// Authentication Types
export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
}

export interface AuthResponse {
  token: string;
  type: string;
  username: string;
  email: string;
  role: 'ADMIN' | 'USER';
  message: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'ADMIN' | 'USER';
  status: string;
  createdAt: string;
  phoneNumber?: string;
}

// Property Types
export interface Property {
  id: number;
  user_id: number;
  category_id: number;
  title: string;
  description: string;
  price: number;
  address: string;
  city: string;
  district: string;
  ward: string;
  latitude?: number;
  longitude?: number;
  property_area: number;
  land_area?: number;
  bedrooms: number;
  bathrooms: number;
  floors?: number;
  property_type: 'APARTMENT' | 'HOUSE' | 'VILLA' | 'LAND' | 'OFFICE' | 'SHOP';
  listing_type: 'SALE' | 'RENT';
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  is_featured: boolean;
  view_count: number;
  contact_count: number;
  published_at?: string;
  expires_at?: string;
  created_at: string;
  updated_at: string;
  category?: Category;
  user?: PropertyUser;
  images?: PropertyImage[];
}

export interface PropertyUser {
  id: number;
  username: string;
  first_name: string;
  last_name: string;
  phone_number: string;
  email?: string;
}

export interface PropertyImage {
  id: number;
  property_id: number;
  image_url: string;
  alt_text?: string;
  display_order: number;
  is_primary: boolean;
}

export interface Category {
  id: number;
  name: string;
  description: string;
  icon: string;
  created_at: string;
  updated_at: string;
}

// Membership Types
export interface Membership {
  id: number;
  name: string;
  description: string;
  price: number;
  duration_months: number;
  max_properties: number;
  featured_properties: number;
  priority_support: boolean;
  analytics_access: boolean;
  multiple_images: boolean;
  contact_info_display: boolean;
  ai_content_generation: boolean;
  push_top_limit: number;
  type: 'BASIC' | 'PREMIUM';
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Dashboard Types
export interface UserDashboard {
  membership: {
    planName: string;
    maxProperties: number;
    propertiesUsed: number;
    propertiesRemaining: number;
    hasAiGeneration: boolean;
    pushTopLimit: number;
    daysRemaining: number;
  };
  properties: {
    totalProperties: number;
    approvedProperties: number;
    pendingProperties: number;
    rejectedProperties: number;
  };
  monthlyUsage: {
    month: string;
    pushTopUsed: number;
    aiContentUsed: number;
  };
}

// Admin Dashboard Types
export interface AdminDashboardStats {
  totalUsers: number;
  totalProperties: number;
  totalPayments: number;
  totalRevenue: number;
  newUsersThisMonth: number;
  newPropertiesThisMonth: number;
  pendingProperties: number;
  activeUsers: number;
}

// Payment Types
export interface PaymentSession {
  sessionId: string;
  url: string;
}

export interface Payment {
  id: number;
  user_id: number;
  membership_id: number;
  amount: number;
  currency: string;
  status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  payment_method: string;
  stripe_session_id?: string;
  created_at: string;
  updated_at: string;
  membership?: Membership;
}

// Chatbot Types
export interface ChatbotMessage {
  message: string;
  conversationId?: string;
}

export interface ChatbotResponse {
  response: string;
  conversationId: string;
  suggestions?: string[];
}

export interface PropertyRecommendationRequest {
  city?: string;
  district?: string;
  property_type?: string;
  listing_type?: string;
  minPrice?: number;
  maxPrice?: number;
  bedrooms?: number;
}

// Analytics Types
export interface PropertyAnalytics {
  period: string;
  totalViews: number;
  totalContacts: number;
  topProperties: Property[];
  viewsByDate: { date: string; views: number }[];
}

export interface UserAnalytics {
  period: string;
  newUsers: number;
  activeUsers: number;
  usersByDate: { date: string; users: number }[];
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: number;
}

export interface PaginatedResponse<T> {
  content: T[];
  pageable: {
    page_number: number;
    page_size: number;
  };
  total_elements: number;
  total_pages: number;
}

// Property Search Parameters
export interface PropertySearchParams {
  page?: number;
  size?: number;
  propertyType?: string;
  listingType?: string;
  city?: string;
  district?: string;
  minPrice?: number;
  maxPrice?: number;
  bedrooms?: number;
  sortBy?: string;
  sortDir?: 'asc' | 'desc';
  query?: string;
}

// Property Form Data
export interface PropertyFormData {
  title: string;
  description: string;
  price: number;
  address: string;
  city: string;
  district: string;
  categoryId: number;
  property_type: string;
  listing_type: string;
  bedrooms: number;
  bathrooms: number;
  property_area: number;
}

// ===== API FUNCTIONS =====

// Authentication API
export const authApi = {
  login: async (credentials: LoginRequest): Promise<AuthResponse> => {
    const response = await apiClient.post<AuthResponse>('/auth/login', credentials);
    return response.data;
  },

  register: async (userData: RegisterRequest): Promise<AuthResponse> => {
    const response = await apiClient.post<AuthResponse>('/auth/register', userData);
    return response.data;
  },

  getUserProfile: async (): Promise<User> => {
    const response = await apiClient.get<User>('/users/profile');
    return response.data;
  },

  updateProfile: async (userData: Partial<User>): Promise<User> => {
    const response = await apiClient.put<User>('/users/profile', userData);
    return response.data;
  },
};

// Admin API
export const adminApi = {
  getDashboardStats: async (): Promise<AdminDashboardStats> => {
    const response = await apiClient.get<AdminDashboardStats>('/admin/dashboard');
    return response.data;
  },

  getUsers: async (page = 0, size = 10): Promise<PaginatedResponse<User>> => {
    const response = await apiClient.get<PaginatedResponse<User>>(`/admin/users?page=${page}&size=${size}`);
    return response.data;
  },

  banUser: async (userId: number): Promise<void> => {
    await apiClient.post(`/admin/users/${userId}/ban`);
  },

  unbanUser: async (userId: number): Promise<void> => {
    await apiClient.post(`/admin/users/${userId}/unban`);
  },

  getPendingProperties: async (page = 0, size = 10): Promise<PaginatedResponse<Property>> => {
    const response = await apiClient.get<PaginatedResponse<Property>>(`/admin/properties/pending?page=${page}&size=${size}`);
    return response.data;
  },

  approveProperty: async (propertyId: number, data?: { isFeatured?: boolean; adminNote?: string }): Promise<void> => {
    await apiClient.post(`/admin/properties/${propertyId}/approve`, data);
  },

  rejectProperty: async (propertyId: number, reason: string): Promise<void> => {
    await apiClient.post(`/admin/properties/${propertyId}/reject`, { reason });
  },
};

// User Dashboard API
export const dashboardApi = {
  getUserDashboard: async (): Promise<UserDashboard> => {
    const response = await apiClient.get<UserDashboard>('/dashboard');
    return response.data;
  },
};

// Property Management API
export const propertyApi = {
  createProperty: async (propertyData: PropertyFormData): Promise<Property> => {
    const response = await apiClient.post<Property>('/properties', propertyData);
    return response.data;
  },

  updateProperty: async (id: number, propertyData: Partial<PropertyFormData>): Promise<Property> => {
    const response = await apiClient.put<Property>(`/properties/${id}`, propertyData);
    return response.data;
  },

  deleteProperty: async (id: number): Promise<void> => {
    await apiClient.delete(`/properties/${id}`);
  },

  getUserProperties: async (page = 0, size = 10): Promise<PaginatedResponse<Property>> => {
    const response = await apiClient.get<PaginatedResponse<Property>>(`/my-properties?page=${page}&size=${size}`);
    return response.data;
  },

  boostProperty: async (id: number): Promise<void> => {
    await apiClient.post(`/properties/${id}/boost`);
  },
};

// Payment API
export const paymentApi = {
  createCheckoutSession: async (membershipId: number, successUrl: string, cancelUrl: string): Promise<PaymentSession> => {
    const response = await apiClient.post<PaymentSession>('/payments/checkout', {
      membershipId,
      successUrl,
      cancelUrl,
    });
    return response.data;
  },

  getUserPayments: async (): Promise<Payment[]> => {
    const response = await apiClient.get<Payment[]>('/payments/my-payments');
    return response.data;
  },
};

// Chatbot API
export const chatbotApi = {
  sendMessage: async (message: string, conversationId?: string): Promise<ChatbotResponse> => {
    const response = await apiClient.post<ChatbotResponse>('/chatbot/message', {
      message,
      conversationId,
    });
    return response.data;
  },

  getRecommendations: async (criteria: PropertyRecommendationRequest): Promise<Property[]> => {
    const response = await apiClient.post<Property[]>('/chatbot/recommendations', criteria);
    return response.data;
  },
};

// Analytics API
export const analyticsApi = {
  getPropertyAnalytics: async (period: 'daily' | 'weekly' | 'monthly' = 'monthly'): Promise<PropertyAnalytics> => {
    const response = await apiClient.get<PropertyAnalytics>(`/analytics/properties?period=${period}`);
    return response.data;
  },

  getUserAnalytics: async (period: 'daily' | 'weekly' | 'monthly' = 'weekly'): Promise<UserAnalytics> => {
    const response = await apiClient.get<UserAnalytics>(`/analytics/users?period=${period}`);
    return response.data;
  },
};

// Public API
export const publicApi = {
  getProperties: async (params: PropertySearchParams = {}): Promise<PaginatedResponse<Property>> => {
    try {
      const queryParams = new URLSearchParams();

      // Set defaults
      queryParams.append('page', (params.page || 0).toString());
      queryParams.append('size', (params.size || 10).toString());

      // Add other parameters if provided
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '' && key !== 'page' && key !== 'size') {
          queryParams.append(key, value.toString());
        }
      });

      const response = await apiClient.get<PaginatedResponse<Property>>(
        `/properties?${queryParams.toString()}`
      );
      return response.data;
    } catch (error) {
      console.log('Backend not available, using mock data');
      const { createMockPaginatedResponse } = await import('./mockData');
      return createMockPaginatedResponse(params.page || 0, params.size || 10);
    }
  },

  getProperty: async (id: number): Promise<Property> => {
    try {
      const response = await apiClient.get<Property>(`/properties/${id}`);
      return response.data;
    } catch (error) {
      console.log('Backend not available, using mock data');
      const { mockProperties } = await import('./mockData');
      const property = mockProperties.find(p => p.id === id);
      if (!property) {
        throw new Error('Property not found');
      }
      return property;
    }
  },

  searchProperties: async (params: PropertySearchParams): Promise<PaginatedResponse<Property>> => {
    try {
      const queryParams = new URLSearchParams();

      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value.toString());
        }
      });

      const response = await apiClient.get<PaginatedResponse<Property>>(
        `/properties/search?${queryParams.toString()}`
      );
      return response.data;
    } catch (error) {
      console.log('Backend not available, using mock data');
      const { createMockPaginatedResponse } = await import('./mockData');
      return createMockPaginatedResponse(params.page || 0, params.size || 10);
    }
  },

  getFeaturedProperties: async (limit = 10): Promise<Property[]> => {
    try {
      const response = await apiClient.get<Property[]>(`/properties/featured?limit=${limit}`);
      return response.data;
    } catch (error) {
      console.log('Backend not available, using mock data');
      const { mockProperties } = await import('./mockData');
      return mockProperties.slice(0, limit);
    }
  },

  getCategories: async (): Promise<Category[]> => {
    try {
      const response = await apiClient.get<Category[]>('/categories');
      return response.data;
    } catch (error) {
      console.log('Backend not available, using mock categories');
      return [
        { id: 1, name: 'Apartment', description: 'Apartment and condominium listings', icon: 'apartment', created_at: '', updated_at: '' },
        { id: 2, name: 'House', description: 'Single-family houses and townhouses', icon: 'house', created_at: '', updated_at: '' },
        { id: 3, name: 'Villa', description: 'Luxury villas and mansions', icon: 'villa', created_at: '', updated_at: '' },
        { id: 4, name: 'Land', description: 'Land plots and development sites', icon: 'land', created_at: '', updated_at: '' },
        { id: 5, name: 'Office', description: 'Office spaces and commercial buildings', icon: 'office', created_at: '', updated_at: '' },
        { id: 6, name: 'Shop', description: 'Retail shops and commercial spaces', icon: 'shop', created_at: '', updated_at: '' }
      ];
    }
  },

  getMemberships: async (): Promise<Membership[]> => {
    try {
      const response = await apiClient.get<Membership[]>('/memberships');
      return response.data;
    } catch (error) {
      console.log('Backend not available, using mock memberships');
      return [
        {
          id: 1,
          name: 'BASIC',
          description: 'Basic membership - Post properties and manage listings',
          price: 99,
          duration_months: 1,
          max_properties: 10,
          featured_properties: 0,
          priority_support: false,
          analytics_access: false,
          multiple_images: false,
          contact_info_display: true,
          ai_content_generation: false,
          push_top_limit: 0,
          type: 'BASIC',
          is_active: true,
          created_at: '',
          updated_at: ''
        },
        {
          id: 2,
          name: 'ADVANCED',
          description: 'Advanced membership - AI SEO optimization, Push top 10 posts, Premium features',
          price: 299,
          duration_months: 1,
          max_properties: 50,
          featured_properties: 10,
          priority_support: true,
          analytics_access: true,
          multiple_images: true,
          contact_info_display: true,
          ai_content_generation: true,
          push_top_limit: 10,
          type: 'PREMIUM',
          is_active: true,
          created_at: '',
          updated_at: ''
        }
      ];
    }
  },
};

// Health check
export const healthApi = {
  checkHealth: async (): Promise<{ status: string; timestamp: string }> => {
    const response = await axios.get(`${API_BASE_URL.replace('/api/v1', '')}/health`);
    return response.data;
  },
};

export default apiClient;
